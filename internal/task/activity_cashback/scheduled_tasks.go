package activity_cashback

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"go.uber.org/zap"
)

// ActivityCashbackScheduledTasks contains all scheduled task functions for Activity Cashback system
type ActivityCashbackScheduledTasks struct {
	service      activity_cashback.ActivityCashbackServiceInterface
	adminService activity_cashback.AdminServiceInterface
}

// NewActivityCashbackScheduledTasks creates a new instance of ActivityCashbackScheduledTasks
func NewActivityCashbackScheduledTasks() *ActivityCashbackScheduledTasks {
	service := activity_cashback.NewActivityCashbackService()
	adminService := activity_cashback.NewAdminService()

	return &ActivityCashbackScheduledTasks{
		service:      service,
		adminService: adminService,
	}
}

// DailyTaskReset resets daily tasks at UTC 00:00
func (t *ActivityCashbackScheduledTasks) DailyTaskReset() {
	global.GVA_LOG.Info("Starting daily task reset job")

	ctx := context.Background()

	// Use the existing ResetDailyTasks method which handles all users
	if err := t.service.ResetDailyTasks(ctx); err != nil {
		global.GVA_LOG.Error("Failed to reset daily tasks", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Daily task reset completed successfully")
}

// TierUpgradeCheck checks for tier upgrades every 30 minutes
func (t *ActivityCashbackScheduledTasks) TierUpgradeCheck() {
	global.GVA_LOG.Info("Starting tier upgrade check job")

	ctx := context.Background()

	// Use admin service to recalculate all user tiers
	if err := t.adminService.RecalculateAllUserTiers(ctx); err != nil {
		global.GVA_LOG.Error("Failed to recalculate user tiers", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Tier upgrade check completed successfully")
}

// CashbackProcessing processes pending cashback claims every 5 minutes
func (t *ActivityCashbackScheduledTasks) CashbackProcessing() {
	global.GVA_LOG.Info("Starting cashback processing job")

	ctx := context.Background()

	// Get pending cashback claims
	claims, err := t.service.GetPendingClaims(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get pending claims", zap.Error(err))
		return
	}

	if len(claims) == 0 {
		global.GVA_LOG.Debug("No pending cashback claims to process")
		return
	}

	global.GVA_LOG.Info("Processing pending cashback claims", zap.Int("count", len(claims)))

	processedCount := 0
	for _, claim := range claims {
		// Process the claim
		if err := t.service.ProcessClaim(ctx, claim.ID); err != nil {
			global.GVA_LOG.Error("Failed to process claim",
				zap.Error(err),
				zap.String("claim_id", claim.ID.String()))
			continue
		}

		// Simulate blockchain transaction (in production, integrate with actual blockchain)
		mockTxHash := "0x" + claim.ID.String()[:40]
		if err := t.service.CompleteClaim(ctx, claim.ID, mockTxHash); err != nil {
			global.GVA_LOG.Error("Failed to complete claim",
				zap.Error(err),
				zap.String("claim_id", claim.ID.String()))
			continue
		}

		processedCount++
	}

	global.GVA_LOG.Info("Cashback processing completed",
		zap.Int("claims_processed", processedCount),
		zap.Int("total_claims", len(claims)))
}

// MonthlyStatsReset resets monthly stats on the 1st of each month
func (t *ActivityCashbackScheduledTasks) MonthlyStatsReset() {
	global.GVA_LOG.Info("Starting monthly stats reset job")

	ctx := context.Background()

	// Use the existing ResetMonthlyStats method which handles all users
	if err := t.service.ResetMonthlyStats(ctx); err != nil {
		global.GVA_LOG.Error("Failed to reset monthly stats", zap.Error(err))
		return
	}

	// Also reset monthly tasks
	if err := t.service.ResetMonthlyTasks(ctx); err != nil {
		global.GVA_LOG.Error("Failed to reset monthly tasks", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Monthly stats reset completed successfully")
}

// TaskProgressCleanup cleans up expired task progress
func (t *ActivityCashbackScheduledTasks) TaskProgressCleanup() {
	global.GVA_LOG.Info("Starting task progress cleanup job")

	// For now, just log that cleanup would happen here
	// In a real implementation, you would add a cleanup method to the service
	global.GVA_LOG.Info("Task progress cleanup job executed - cleanup logic would be implemented here")
}

// CommunityTasksProcessing processes pending community tasks
func (t *ActivityCashbackScheduledTasks) CommunityTasksProcessing() {
	global.GVA_LOG.Debug("Starting community tasks processing job")

	ctx := context.Background()

	// Use the existing ProcessPendingCommunityTasks method
	if err := t.service.ProcessPendingCommunityTasks(ctx); err != nil {
		global.GVA_LOG.Error("Failed to process pending community tasks", zap.Error(err))
		return
	}

	global.GVA_LOG.Debug("Community tasks processing completed")
}
