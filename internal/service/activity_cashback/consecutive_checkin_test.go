package activity_cashback

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestConsecutiveCheckinLogic tests the consecutive check-in logic without database
func TestConsecutiveCheckinLogic(t *testing.T) {
	t.Run("Task_Identifiers_Defined", func(t *testing.T) {
		// Test that all task identifiers are properly defined
		assert.Equal(t, "CONSECUTIVE_CHECKIN_3", string(model.TaskIDConsecutiveCheckin3))
		assert.Equal(t, "CONSECUTIVE_CHECKIN_7", string(model.TaskIDConsecutiveCheckin7))
		assert.Equal(t, "CONSECUTIVE_CHECKIN_30", string(model.TaskIDConsecutiveCheckin30))
	})

	t.Run("Task_Definitions_Exist", func(t *testing.T) {
		// Test that task definitions exist in registry
		def3, exists := model.TaskDefinitionRegistry[model.TaskIDConsecutiveCheckin3]
		assert.True(t, exists, "3-day consecutive check-in task definition should exist")
		assert.Equal(t, 50, def3.Points, "3-day task should award 50 points")

		def7, exists := model.TaskDefinitionRegistry[model.TaskIDConsecutiveCheckin7]
		assert.True(t, exists, "7-day consecutive check-in task definition should exist")
		assert.Equal(t, 200, def7.Points, "7-day task should award 200 points")

		def30, exists := model.TaskDefinitionRegistry[model.TaskIDConsecutiveCheckin30]
		assert.True(t, exists, "30-day consecutive check-in task definition should exist")
		assert.Equal(t, 1000, def30.Points, "30-day task should award 1000 points")
	})
}

// TestConsecutiveCheckinHandlerLogic tests the handler logic without database
func TestConsecutiveCheckinHandlerLogic(t *testing.T) {
	t.Run("Handler_Types_Exist", func(t *testing.T) {
		// Test that handler types are properly defined
		service := &MockActivityCashbackService{}

		handler3 := NewConsecutiveCheckin3Handler(service)
		assert.NotNil(t, handler3, "3-day handler should be created")
		assert.Equal(t, model.TaskIDConsecutiveCheckin3, handler3.identifier)

		handler7 := NewConsecutiveCheckin7Handler(service)
		assert.NotNil(t, handler7, "7-day handler should be created")
		assert.Equal(t, model.TaskIDConsecutiveCheckin7, handler7.identifier)

		handler30 := NewConsecutiveCheckin30Handler(service)
		assert.NotNil(t, handler30, "30-day handler should be created")
		assert.Equal(t, model.TaskIDConsecutiveCheckin30, handler30.identifier)
	})
}

// TestMilestoneRewards tests that correct points are awarded for milestones
func TestMilestoneRewards(t *testing.T) {
	expectedRewards := map[int]int{
		3:  50,
		7:  200,
		30: 1000,
	}

	for days, expectedPoints := range expectedRewards {
		t.Run(fmt.Sprintf("Milestone_%d_Days", days), func(t *testing.T) {
			assert.Equal(t, expectedPoints, expectedRewards[days],
				"Milestone reward for %d consecutive days should be %d points",
				days, expectedPoints)
		})
	}
}

// TestTaskVisibilityLogic tests the task visibility logic
func TestTaskVisibilityLogic(t *testing.T) {
	ctx := context.Background()
	userID := uuid.New()

	service := NewActivityCashbackService()

	t.Run("Only_One_Task_Visible_At_Time", func(t *testing.T) {
		// Get all consecutive check-in tasks
		tasks, err := service.GetTasksByCategory(ctx, model.CategoryDaily)
		require.NoError(t, err)

		var consecutiveTasks []model.ActivityTask
		for _, task := range tasks {
			if task.TaskIdentifier != nil {
				switch *task.TaskIdentifier {
				case model.TaskIDConsecutiveCheckin3, model.TaskIDConsecutiveCheckin7, model.TaskIDConsecutiveCheckin30:
					consecutiveTasks = append(consecutiveTasks, task)
				}
			}
		}

		if len(consecutiveTasks) == 0 {
			t.Skip("No consecutive check-in tasks found")
		}

		// Get task center data
		taskCenter, err := service.GetTaskCenter(ctx, userID)
		require.NoError(t, err)

		// Count visible consecutive check-in tasks
		visibleConsecutiveTasks := 0
		for _, category := range taskCenter.Categories {
			if category.Category.Name == model.CategoryDaily {
				for _, taskWithProgress := range category.Tasks {
					if taskWithProgress.Task.TaskIdentifier != nil {
						switch *taskWithProgress.Task.TaskIdentifier {
						case model.TaskIDConsecutiveCheckin3, model.TaskIDConsecutiveCheckin7, model.TaskIDConsecutiveCheckin30:
							visibleConsecutiveTasks++
						}
					}
				}
			}
		}

		// Should only have 0 or 1 visible consecutive check-in task
		assert.LessOrEqual(t, visibleConsecutiveTasks, 1,
			"Should have at most 1 visible consecutive check-in task, got %d", visibleConsecutiveTasks)
	})
}

// TestTaskProgression tests the full progression from 3 -> 7 -> 30 days
func TestTaskProgression(t *testing.T) {
	t.Run("Full_Progression_3_7_30", func(t *testing.T) {
		// This is an integration test that would require database setup
		// For now, we'll test the logic components

		// Test progression logic
		progressions := []struct {
			currentStreak int
			expectedTask  model.TaskIdentifier
		}{
			{0, model.TaskIDConsecutiveCheckin3},
			{1, model.TaskIDConsecutiveCheckin3},
			{2, model.TaskIDConsecutiveCheckin3},
			{3, model.TaskIDConsecutiveCheckin7},
			{4, model.TaskIDConsecutiveCheckin7},
			{6, model.TaskIDConsecutiveCheckin7},
			{7, model.TaskIDConsecutiveCheckin30},
			{15, model.TaskIDConsecutiveCheckin30},
			{30, model.TaskIdentifier("")}, // No task after 30 days
		}

		for _, prog := range progressions {
			t.Run(fmt.Sprintf("Streak_%d", prog.currentStreak), func(t *testing.T) {
				// This would test the logic if we had a way to mock the streak
				// For now, we verify the expected behavior
				if prog.currentStreak < 3 {
					assert.Equal(t, model.TaskIDConsecutiveCheckin3, prog.expectedTask)
				} else if prog.currentStreak < 7 {
					assert.Equal(t, model.TaskIDConsecutiveCheckin7, prog.expectedTask)
				} else if prog.currentStreak < 30 {
					assert.Equal(t, model.TaskIDConsecutiveCheckin30, prog.expectedTask)
				}
			})
		}
	})
}
