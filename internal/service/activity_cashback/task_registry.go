package activity_cashback

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

// TaskHandler defines the interface for handling specific tasks
type TaskHandler interface {
	Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error
	GetIdentifier() model.TaskIdentifier
	GetCategory() string
}

// TaskRegistry manages task handlers
type TaskRegistry struct {
	handlers map[model.TaskIdentifier]TaskHandler
	service  ActivityCashbackServiceInterface
}

// NewTaskRegistry creates a new task registry
func NewTaskRegistry(service ActivityCashbackServiceInterface) *TaskRegistry {
	registry := &TaskRegistry{
		handlers: make(map[model.TaskIdentifier]TaskHandler),
		service:  service,
	}

	// Register all task handlers
	registry.registerHandlers()

	return registry
}

// registerHandlers registers all task handlers
func (r *TaskRegistry) registerHandlers() {
	// Daily task handlers
	r.RegisterHandler(NewDailyCheckinHandler(r.service))
	r.RegisterHandler(NewMemeTradeHandler(r.service))
	r.RegisterHandler(NewPerpetualTradeHandler(r.service))
	r.RegisterHandler(NewMarketPageViewHandler(r.service))
	r.RegisterHandler(NewConsecutiveCheckin3Handler(r.service))
	r.RegisterHandler(NewConsecutiveCheckin7Handler(r.service))
	r.RegisterHandler(NewConsecutiveCheckin30Handler(r.service))
	r.RegisterHandler(NewConsecutiveTradingDaysHandler(r.service))

	// Community task handlers
	r.RegisterHandler(NewTwitterFollowHandler(r.service))
	r.RegisterHandler(NewTwitterRetweetHandler(r.service))
	r.RegisterHandler(NewTwitterLikeHandler(r.service))
	r.RegisterHandler(NewTelegramJoinHandler(r.service))
	r.RegisterHandler(NewInviteFriendsHandler(r.service))
	r.RegisterHandler(NewShareReferralHandler(r.service))
	r.RegisterHandler(NewShareEarningsChartHandler(r.service))

	// Trading task handlers
	r.RegisterHandler(NewTradingMemeTradeHandler(r.service))
	r.RegisterHandler(NewTradingPerpetualTradeHandler(r.service))
	r.RegisterHandler(NewTradingPointsHandler(r.service))
	r.RegisterHandler(NewAccumulatedTradingHandler(r.service, model.TaskIDAccumulatedTrading10K, 10))
	r.RegisterHandler(NewAccumulatedTradingHandler(r.service, model.TaskIDAccumulatedTrading50K, 50))
	r.RegisterHandler(NewAccumulatedTradingHandler(r.service, model.TaskIDAccumulatedTrading100K, 100))
	r.RegisterHandler(NewAccumulatedTradingHandler(r.service, model.TaskIDAccumulatedTrading500K, 500))
}

// RegisterHandler registers a task handler
func (r *TaskRegistry) RegisterHandler(handler TaskHandler) {
	r.handlers[handler.GetIdentifier()] = handler
}

// GetHandler returns a handler for the given task identifier
func (r *TaskRegistry) GetHandler(identifier model.TaskIdentifier) (TaskHandler, bool) {
	handler, exists := r.handlers[identifier]
	return handler, exists
}

// ProcessTask processes a task using the appropriate handler
func (r *TaskRegistry) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Task must have TaskIdentifier - no fallback for best practices
	if task.TaskIdentifier == nil {
		return fmt.Errorf("task missing required identifier: %s", task.Name)
	}

	identifier := *task.TaskIdentifier

	// Validate task identifier exists in our registry
	if !model.IsValidTaskIdentifier(identifier) {
		return fmt.Errorf("invalid task identifier: %s", identifier)
	}

	// Get handler for the identifier
	handler, exists := r.GetHandler(identifier)
	if !exists {
		return fmt.Errorf("no handler found for task identifier: %s", identifier)
	}

	// Process the task
	return handler.Handle(ctx, userID, task, data)
}

// ProcessTaskByIdentifier processes a task by its identifier
func (r *TaskRegistry) ProcessTaskByIdentifier(ctx context.Context, userID uuid.UUID, identifier model.TaskIdentifier, categoryName model.TaskCategoryName, data map[string]interface{}) error {
	// Validate identifier first
	if !model.IsValidTaskIdentifier(identifier) {
		return fmt.Errorf("invalid task identifier: %s", identifier)
	}

	// Get tasks by category
	tasks, err := r.service.GetTasksByCategory(ctx, categoryName)
	if err != nil {
		return fmt.Errorf("failed to get tasks by category: %w", err)
	}

	// Find all tasks with the specified identifier
	var matchingTasks []model.ActivityTask
	global.GVA_LOG.Debug("Searching for tasks by identifier",
		zap.String("identifier", string(identifier)),
		zap.String("category", string(categoryName)),
		zap.Int("total_tasks", len(tasks)))

	for _, task := range tasks {
		global.GVA_LOG.Debug("Checking task",
			zap.String("task_id", task.ID.String()),
			zap.String("task_name", task.Name),
			zap.Any("task_identifier", task.TaskIdentifier),
			zap.Uint("category_id", task.CategoryID))

		if task.TaskIdentifier != nil && *task.TaskIdentifier == identifier {
			matchingTasks = append(matchingTasks, task)
			global.GVA_LOG.Info("Found matching task",
				zap.String("task_id", task.ID.String()),
				zap.String("task_name", task.Name),
				zap.String("identifier", string(identifier)),
				zap.String("category", string(categoryName)))
		}
	}

	if len(matchingTasks) == 0 {
		return fmt.Errorf("no tasks found with identifier %s in category %s", identifier, categoryName)
	}

	// Process all matching tasks
	var errors []error
	successCount := 0

	global.GVA_LOG.Info("Processing multiple tasks with same identifier",
		zap.String("identifier", string(identifier)),
		zap.String("category", string(categoryName)),
		zap.Int("task_count", len(matchingTasks)))

	for _, task := range matchingTasks {
		taskCopy := task // Create a copy to avoid pointer issues
		if err := r.ProcessTask(ctx, userID, &taskCopy, data); err != nil {
			global.GVA_LOG.Warn("Failed to process task",
				zap.String("task_id", task.ID.String()),
				zap.String("task_name", task.Name),
				zap.Error(err))
			errors = append(errors, fmt.Errorf("task %s (%s): %w", task.ID.String(), task.Name, err))
		} else {
			successCount++
			global.GVA_LOG.Info("Successfully processed task",
				zap.String("task_id", task.ID.String()),
				zap.String("task_name", task.Name),
				zap.Int("points", task.Points))
		}
	}

	global.GVA_LOG.Info("Task processing summary",
		zap.String("identifier", string(identifier)),
		zap.String("category", string(categoryName)),
		zap.Int("total_tasks", len(matchingTasks)),
		zap.Int("successful", successCount),
		zap.Int("failed", len(errors)))

	// Return success if at least one task was processed successfully
	if successCount > 0 {
		if len(errors) > 0 {
			global.GVA_LOG.Warn("Some tasks failed but at least one succeeded",
				zap.String("identifier", string(identifier)),
				zap.Int("successful", successCount),
				zap.Int("failed", len(errors)))
		}
		return nil
	}

	// All tasks failed
	if len(errors) == 1 {
		return errors[0]
	}
	return fmt.Errorf("all %d tasks failed: %v", len(errors), errors)
}

// CreateTaskFromDefinition creates a task from its definition
func (r *TaskRegistry) CreateTaskFromDefinition(ctx context.Context, identifier model.TaskIdentifier, categoryID uint) (*model.ActivityTask, error) {
	definition, exists := model.GetTaskDefinition(identifier)
	if !exists {
		return nil, fmt.Errorf("task definition not found: %s", identifier)
	}

	task := &model.ActivityTask{
		CategoryID:     categoryID,
		Name:           definition.DisplayName,
		Description:    &definition.Description,
		TaskIdentifier: &identifier,
		Points:         definition.Points,

		Frequency: model.FrequencyDaily, // Default, can be overridden
		IsActive:  true,
	}

	return task, nil
}

// GetAllHandlers returns all registered handlers
func (r *TaskRegistry) GetAllHandlers() map[model.TaskIdentifier]TaskHandler {
	return r.handlers
}
