package activity_cashback

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// MockActivityCashbackService is a mock implementation for testing
type MockActivityCashbackService struct {
	mock.Mock
}

func (m *MockActivityCashbackService) InitializeUserForActivityCashback(ctx context.Context, userID uuid.UUID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockActivityCashbackService) GetUserDashboard(ctx context.Context, userID uuid.UUID) (*UserDashboard, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*UserDashboard), args.Error(1)
}

func (m *MockActivityCashbackService) GetTaskCenter(ctx context.Context, userID uuid.UUID) (*TaskCenter, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*TaskCenter), args.Error(1)
}

func (m *MockActivityCashbackService) GetActivityCashbackSummary(ctx context.Context, userID uuid.UUID) (*ActivityCashbackSummary, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*ActivityCashbackSummary), args.Error(1)
}

func (m *MockActivityCashbackService) RefreshTaskList(ctx context.Context, userID uuid.UUID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockActivityCashbackService) GetUserTaskListByCategoryWithDetails(ctx context.Context, userID uuid.UUID, categoryName model.TaskCategoryName) ([]TaskWithProgress, error) {
	args := m.Called(ctx, userID, categoryName)
	return args.Get(0).([]TaskWithProgress), args.Error(1)
}

func (m *MockActivityCashbackService) CreateTask(ctx context.Context, task *model.ActivityTask) error {
	args := m.Called(ctx, task)
	return args.Error(0)
}

func (m *MockActivityCashbackService) UpdateTask(ctx context.Context, task *model.ActivityTask) error {
	args := m.Called(ctx, task)
	return args.Error(0)
}

func (m *MockActivityCashbackService) DeleteTask(ctx context.Context, taskID uuid.UUID) error {
	args := m.Called(ctx, taskID)
	return args.Error(0)
}

func (m *MockActivityCashbackService) GetTaskByID(ctx context.Context, taskID uuid.UUID) (*model.ActivityTask, error) {
	args := m.Called(ctx, taskID)
	return args.Get(0).(*model.ActivityTask), args.Error(1)
}

func (m *MockActivityCashbackService) GetTasksForUser(ctx context.Context, userID uuid.UUID) ([]model.ActivityTask, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]model.ActivityTask), args.Error(1)
}

func (m *MockActivityCashbackService) GetTasksByCategory(ctx context.Context, categoryName model.TaskCategoryName) ([]model.ActivityTask, error) {
	args := m.Called(ctx, categoryName)
	return args.Get(0).([]model.ActivityTask), args.Error(1)
}

func (m *MockActivityCashbackService) CompleteTask(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) error {
	args := m.Called(ctx, userID, taskID, verificationData)
	return args.Error(0)
}

func (m *MockActivityCashbackService) CompleteTaskWithPoints(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) error {
	args := m.Called(ctx, userID, taskID, verificationData)
	return args.Error(0)
}

func (m *MockActivityCashbackService) AddTradingVolume(ctx context.Context, userID uuid.UUID, volume decimal.Decimal) error {
	args := m.Called(ctx, userID, volume)
	return args.Error(0)
}

func (m *MockActivityCashbackService) UpdateTaskProgress(ctx context.Context, userID, taskID uuid.UUID, progressValue int) error {
	args := m.Called(ctx, userID, taskID, progressValue)
	return args.Error(0)
}

func (m *MockActivityCashbackService) ClaimTaskReward(ctx context.Context, userID, taskID uuid.UUID) error {
	args := m.Called(ctx, userID, taskID)
	return args.Error(0)
}

func (m *MockActivityCashbackService) RefreshUserTasks(ctx context.Context, userID uuid.UUID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockActivityCashbackService) ResetDailyTasks(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockActivityCashbackService) ResetWeeklyTasks(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockActivityCashbackService) ResetMonthlyTasks(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockActivityCashbackService) VerifyTaskCompletion(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) (bool, error) {
	args := m.Called(ctx, userID, taskID, verificationData)
	return args.Bool(0), args.Error(1)
}

func (m *MockActivityCashbackService) VerifySocialMediaTask(ctx context.Context, userID, taskID uuid.UUID, socialData map[string]interface{}) (bool, error) {
	args := m.Called(ctx, userID, taskID, socialData)
	return args.Bool(0), args.Error(1)
}

func (m *MockActivityCashbackService) VerifyTradingTask(ctx context.Context, userID, taskID uuid.UUID, tradingData map[string]interface{}) (bool, error) {
	args := m.Called(ctx, userID, taskID, tradingData)
	return args.Bool(0), args.Error(1)
}

// Additional methods from task_registry_test.go
func (m *MockActivityCashbackService) CompleteProgress(ctx context.Context, userID, taskID uuid.UUID) error {
	args := m.Called(ctx, userID, taskID)
	return args.Error(0)
}

func (m *MockActivityCashbackService) UpdateActivity(ctx context.Context, userID uuid.UUID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockActivityCashbackService) GetTaskProgress(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error) {
	args := m.Called(ctx, userID, taskID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.UserTaskProgress), args.Error(1)
}

// TierManagementServiceInterface methods
func (m *MockActivityCashbackService) GetUserTierInfo(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.UserTierInfo), args.Error(1)
}

func (m *MockActivityCashbackService) CreateUserTierInfo(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.UserTierInfo), args.Error(1)
}

func (m *MockActivityCashbackService) UpdateUserTierInfo(ctx context.Context, tierInfo *model.UserTierInfo) error {
	args := m.Called(ctx, tierInfo)
	return args.Error(0)
}

func (m *MockActivityCashbackService) AddPoints(ctx context.Context, userID uuid.UUID, points int, source string) error {
	args := m.Called(ctx, userID, points, source)
	return args.Error(0)
}

func (m *MockActivityCashbackService) GetUserPoints(ctx context.Context, userID uuid.UUID) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

func (m *MockActivityCashbackService) GetUserRank(ctx context.Context, userID uuid.UUID) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

func (m *MockActivityCashbackService) CheckTierUpgrade(ctx context.Context, userID uuid.UUID) (*model.TierBenefit, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.TierBenefit), args.Error(1)
}

func (m *MockActivityCashbackService) UpgradeUserTier(ctx context.Context, userID uuid.UUID, newTier int) error {
	args := m.Called(ctx, userID, newTier)
	return args.Error(0)
}

func (m *MockActivityCashbackService) GetNextTierRequirement(ctx context.Context, userID uuid.UUID) (*model.TierBenefit, int, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.TierBenefit), args.Int(1), args.Error(2)
}

func (m *MockActivityCashbackService) TriggerTierUpgradeCheck(ctx context.Context, userID uuid.UUID) (*model.TierBenefit, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.TierBenefit), args.Error(1)
}

func (m *MockActivityCashbackService) AddCashback(ctx context.Context, userID uuid.UUID, amount decimal.Decimal) error {
	args := m.Called(ctx, userID, amount)
	return args.Error(0)
}

func (m *MockActivityCashbackService) GetClaimableCashback(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockActivityCashbackService) ClaimCashback(ctx context.Context, userID uuid.UUID, amount decimal.Decimal) error {
	args := m.Called(ctx, userID, amount)
	return args.Error(0)
}

func (m *MockActivityCashbackService) ResetMonthlyStats(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockActivityCashbackService) GetMonthlyStats(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.UserTierInfo), args.Error(1)
}

// TaskProgressServiceInterface methods
func (m *MockActivityCashbackService) GetUserTaskProgress(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]model.UserTaskProgress), args.Error(1)
}

func (m *MockActivityCashbackService) InitializeTaskProgress(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error) {
	args := m.Called(ctx, userID, taskID)
	return args.Get(0).(*model.UserTaskProgress), args.Error(1)
}

func (m *MockActivityCashbackService) IncrementProgress(ctx context.Context, userID, taskID uuid.UUID, increment int) error {
	args := m.Called(ctx, userID, taskID, increment)
	return args.Error(0)
}

func (m *MockActivityCashbackService) SetProgress(ctx context.Context, userID, taskID uuid.UUID, value int) error {
	args := m.Called(ctx, userID, taskID, value)
	return args.Error(0)
}

func (m *MockActivityCashbackService) UpdateStreak(ctx context.Context, userID, taskID uuid.UUID, increment bool) error {
	args := m.Called(ctx, userID, taskID, increment)
	return args.Error(0)
}

func (m *MockActivityCashbackService) ResetStreak(ctx context.Context, userID, taskID uuid.UUID) error {
	args := m.Called(ctx, userID, taskID)
	return args.Error(0)
}

func (m *MockActivityCashbackService) GetUserStreaks(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]model.UserTaskProgress), args.Error(1)
}

func (m *MockActivityCashbackService) GetCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error) {
	args := m.Called(ctx, userID, startDate, endDate)
	return args.Get(0).(map[string]int), args.Error(1)
}

func (m *MockActivityCashbackService) GetTaskCompletionRate(ctx context.Context, taskID uuid.UUID) (float64, error) {
	args := m.Called(ctx, taskID)
	return args.Get(0).(float64), args.Error(1)
}

// CashbackClaimServiceInterface methods
func (m *MockActivityCashbackService) CreateClaim(ctx context.Context, userID uuid.UUID, claimType model.ClaimType, amountUSD, amountSOL decimal.Decimal, metadata *model.ClaimMetadata) (*model.ActivityCashbackClaim, error) {
	args := m.Called(ctx, userID, claimType, amountUSD, amountSOL, metadata)
	return args.Get(0).(*model.ActivityCashbackClaim), args.Error(1)
}

func (m *MockActivityCashbackService) ProcessClaim(ctx context.Context, claimID uuid.UUID) error {
	args := m.Called(ctx, claimID)
	return args.Error(0)
}

func (m *MockActivityCashbackService) CompleteClaim(ctx context.Context, claimID uuid.UUID, transactionHash string) error {
	args := m.Called(ctx, claimID, transactionHash)
	return args.Error(0)
}

func (m *MockActivityCashbackService) FailClaim(ctx context.Context, claimID uuid.UUID, errorDetails map[string]interface{}) error {
	args := m.Called(ctx, claimID, errorDetails)
	return args.Error(0)
}

func (m *MockActivityCashbackService) GetUserClaims(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ActivityCashbackClaim, error) {
	args := m.Called(ctx, userID, limit, offset)
	return args.Get(0).([]model.ActivityCashbackClaim), args.Error(1)
}

func (m *MockActivityCashbackService) GetClaimByID(ctx context.Context, claimID uuid.UUID) (*model.ActivityCashbackClaim, error) {
	args := m.Called(ctx, claimID)
	return args.Get(0).(*model.ActivityCashbackClaim), args.Error(1)
}

func (m *MockActivityCashbackService) GetPendingClaims(ctx context.Context) ([]model.ActivityCashbackClaim, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.ActivityCashbackClaim), args.Error(1)
}

func (m *MockActivityCashbackService) GetUserClaimHistory(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) ([]model.ActivityCashbackClaim, error) {
	args := m.Called(ctx, userID, startDate, endDate)
	return args.Get(0).([]model.ActivityCashbackClaim), args.Error(1)
}

func (m *MockActivityCashbackService) GetTotalClaimedAmount(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockActivityCashbackService) GetClaimStats(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error) {
	args := m.Called(ctx, startDate, endDate)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

// Pending community task operations
func (m *MockActivityCashbackService) CreatePendingCommunityTask(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) (*model.PendingCommunityTask, error) {
	args := m.Called(ctx, userID, taskID, verificationData)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.PendingCommunityTask), args.Error(1)
}

func (m *MockActivityCashbackService) GetPendingCommunityTask(ctx context.Context, userID, taskID uuid.UUID) (*model.PendingCommunityTask, error) {
	args := m.Called(ctx, userID, taskID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.PendingCommunityTask), args.Error(1)
}

func (m *MockActivityCashbackService) HasPendingCommunityTask(ctx context.Context, userID, taskID uuid.UUID) (bool, error) {
	args := m.Called(ctx, userID, taskID)
	return args.Bool(0), args.Error(1)
}

func (m *MockActivityCashbackService) ProcessPendingCommunityTasks(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockActivityCashbackService) ProcessSinglePendingTask(ctx context.Context, pendingTask *model.PendingCommunityTask) error {
	args := m.Called(ctx, pendingTask)
	return args.Error(0)
}

func (m *MockActivityCashbackService) GetVisibleConsecutiveCheckinTask(ctx context.Context, userID uuid.UUID) (*model.ActivityTask, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.ActivityTask), args.Error(1)
}

func (m *MockActivityCashbackService) CompleteTaskProgressOnly(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) error {
	args := m.Called(ctx, userID, taskID, verificationData)
	return args.Error(0)
}

func (m *MockActivityCashbackService) ProcessTaskWithRegistry(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	args := m.Called(ctx, userID, task, data)
	return args.Error(0)
}
