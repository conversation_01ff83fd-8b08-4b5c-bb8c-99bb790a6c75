package level

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
)

type LevelService struct {
	levelRepo repo.LevelRepo
}

func NewLevelService() service.LevelI {
	return &LevelService{
		levelRepo: repo.NewLevelRepository(),
	}
}

// GetAgentLevels retrieves all agent levels
func (s *LevelService) GetAgentLevels(ctx context.Context) ([]model.AgentLevel, error) {
	return s.levelRepo.GetAgentLevels(ctx)
}

// GetAgentLevelByID retrieves a specific agent level by ID
func (s *LevelService) GetAgentLevelByID(ctx context.Context, id uint) (*model.AgentLevel, error) {
	return s.levelRepo.GetAgentLevelByID(ctx, id)
}

// CreateAgentLevel creates a new agent level with validation
func (s *LevelService) CreateAgentLevel(ctx context.Context, level *model.AgentLevel) (*model.AgentLevel, error) {
	// Validate required fields
	if level.Name == "" {
		return nil, fmt.Errorf("agent level name is required")
	}

	// Validate commission rates are within valid range (0-1)
	directRate, _ := level.DirectCommissionRate.Float64()
	indirectRate, _ := level.IndirectCommissionRate.Float64()
	extendedRate, _ := level.ExtendedCommissionRate.Float64()
	memeFeeRebate, _ := level.MemeFeeRebate.Float64()

	if directRate < 0 || directRate > 1 {
		return nil, fmt.Errorf("direct commission rate must be between 0 and 1")
	}
	if indirectRate < 0 || indirectRate > 1 {
		return nil, fmt.Errorf("indirect commission rate must be between 0 and 1")
	}
	if extendedRate < 0 || extendedRate > 1 {
		return nil, fmt.Errorf("extended commission rate must be between 0 and 1")
	}
	if memeFeeRebate < 0 || memeFeeRebate > 1 {
		return nil, fmt.Errorf("meme fee rebate must be between 0 and 1")
	}

	// Create the level
	err := s.levelRepo.CreateAgentLevel(ctx, level)
	if err != nil {
		return nil, fmt.Errorf("failed to create agent level: %w", err)
	}

	return level, nil
}

// UpdateAgentLevel updates all fields of an agent level
func (s *LevelService) UpdateAgentLevel(ctx context.Context, level *model.AgentLevel) (*model.AgentLevel, error) {
	// Check if level exists
	_, err := s.levelRepo.GetAgentLevelByID(ctx, level.ID)
	if err != nil {
		return nil, fmt.Errorf("agent level not found: %w", err)
	}

	// Validate required fields
	if level.Name == "" {
		return nil, fmt.Errorf("agent level name is required")
	}

	// Validate commission rates are within valid range (0-1)
	directRate, _ := level.DirectCommissionRate.Float64()
	indirectRate, _ := level.IndirectCommissionRate.Float64()
	extendedRate, _ := level.ExtendedCommissionRate.Float64()
	memeFeeRebate, _ := level.MemeFeeRebate.Float64()

	if directRate < 0 || directRate > 1 {
		return nil, fmt.Errorf("direct commission rate must be between 0 and 1")
	}
	if indirectRate < 0 || indirectRate > 1 {
		return nil, fmt.Errorf("indirect commission rate must be between 0 and 1")
	}
	if extendedRate < 0 || extendedRate > 1 {
		return nil, fmt.Errorf("extended commission rate must be between 0 and 1")
	}
	if memeFeeRebate < 0 || memeFeeRebate > 1 {
		return nil, fmt.Errorf("meme fee rebate must be between 0 and 1")
	}

	// Update the level
	err = s.levelRepo.UpdateAgentLevel(ctx, level)
	if err != nil {
		return nil, fmt.Errorf("failed to update agent level: %w", err)
	}

	return level, nil
}

// DeleteAgentLevel deletes an agent level with validation
func (s *LevelService) DeleteAgentLevel(ctx context.Context, id uint) error {
	// Check if level exists
	_, err := s.levelRepo.GetAgentLevelByID(ctx, id)
	if err != nil {
		return fmt.Errorf("agent level not found: %w", err)
	}

	// Check if any users are using this level
	userCount, err := s.levelRepo.CountUsersByAgentLevel(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to check users using this level: %w", err)
	}

	if userCount > 0 {
		return fmt.Errorf("cannot delete agent level: %d users are currently using this level", userCount)
	}

	// Prevent deletion of default level (ID = 1)
	if id == 1 {
		return fmt.Errorf("cannot delete default agent level (Lv1)")
	}

	// Delete the level
	err = s.levelRepo.DeleteAgentLevel(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete agent level: %w", err)
	}

	return nil
}

// GetUserLevelInfo retrieves user's current level information including volume and next level requirements
func (s *LevelService) GetUserLevelInfo(ctx context.Context, userID uuid.UUID) (*response.UserLevelInfoResponse, error) {
	// Get user with current level
	var user model.User
	err := global.GVA_DB.WithContext(ctx).Preload("AgentLevel").Where("id = ?", userID).First(&user).Error
	if err != nil {
		return &response.UserLevelInfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to get user: %v", err),
		}, nil
	}

	// yesterday := time.Now().UTC().AddDate(0, 0, -1)
	// thirtyDaysAgo := yesterday.AddDate(0, 0, -29)

	// memeVolume, contractVolume, err := s.calculateUserVolume(userID, thirtyDaysAgo, yesterday)
	// if err != nil {
	// 	return &response.UserLevelInfoResponse{
	// 		Success: false,
	// 		Message: fmt.Sprintf("Failed to calculate user volume: %v", err),
	// 	}, nil
	// }

	memeFeeRebate, err := s.calculateMemeFeeRebate(ctx, userID)
	if err != nil {
		return &response.UserLevelInfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to calculate meme fee rebate: %v", err),
		}, nil
	}

	contractFeeRebate, err := s.calculateContractFeeRebate(ctx, userID)
	if err != nil {
		return &response.UserLevelInfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to calculate contract fee rebate: %v", err),
		}, nil
	}

	// Get all levels to find next level
	// levels, err := s.GetAgentLevels(ctx)
	// if err != nil {
	// 	return &response.UserLevelInfoResponse{
	// 		Success: false,
	// 		Message: fmt.Sprintf("Failed to get agent levels: %v", err),
	// 	}, nil
	// }

	// Find next level and calculate volume requirements
	// nextLevel, volumeToNextLevel, memeVolumeToNextLevel, contractVolumeToNextLevel, isMaxLevel := s.findNextLevelAndRequirements(
	// 	memeVolume, contractVolume, user.AgentLevel, levels)

	// Convert decimal values to float64
	// memeVolumeFloat, _ := memeVolume.Float64()
	// contractVolumeFloat, _ := contractVolume.Float64()
	// totalVolumeFloat := memeVolumeFloat + contractVolumeFloat

	// TODO needs to be converted to SQL
	memeFeeRebateFloat, _ := memeFeeRebate.Float64()
	contractFeeRebateFloat, _ := contractFeeRebate.Float64()
	totalCommissionEarnedFloat := memeFeeRebateFloat + contractFeeRebateFloat

	userLevelInfo := &response.UserLevelInfo{
		CurrentLevel:   &user.AgentLevel,
		MemeVolume:     memeFeeRebateFloat,
		ContractVolume: contractFeeRebateFloat,
		TotalVolume:    totalCommissionEarnedFloat,
		// NextLevel:                 nextLevel,
		// VolumeToNextLevel:         volumeToNextLevel,
		// MemeVolumeToNextLevel:     memeVolumeToNextLevel,
		// ContractVolumeToNextLevel: contractVolumeToNextLevel,
		// IsMaxLevel:                isMaxLevel,
	}

	return &response.UserLevelInfoResponse{
		Success: true,
		Message: "Successfully retrieved user level information",
		Data:    userLevelInfo,
	}, nil
}

// calculateUserVolume calculates user's MEME and contract transaction volumes within specified time period
func (s *LevelService) calculateUserVolume(userID uuid.UUID, startDate, endDate time.Time) (decimal.Decimal, decimal.Decimal, error) {
	// Calculate MEME transaction volume from daily_meme_volumes table
	var memeVolume decimal.Decimal
	err := global.GVA_DB.Model(&model.DailyMemeVolume{}).
		Where("user_id = ? AND date >= ? AND date <= ?", userID, startDate, endDate).
		Select("COALESCE(SUM(meme_volume_usd), 0)").
		Scan(&memeVolume).Error

	if err != nil {
		return decimal.Zero, decimal.Zero, err
	}

	// Calculate contract transaction volume from daily_user_volumes table
	var contractVolume decimal.Decimal
	err = global.GVA_DB.Model(&model.DailyUserVolume{}).
		Where("user_id = ? AND date >= ? AND date <= ?", userID, startDate, endDate).
		Select("COALESCE(SUM(contract_volume_usd), 0)").
		Scan(&contractVolume).Error

	if err != nil {
		return decimal.Zero, decimal.Zero, err
	}

	return memeVolume, contractVolume, nil
}

// findNextLevelAndRequirements finds the next level and calculates volume requirements
func (s *LevelService) findNextLevelAndRequirements(memeVolume, contractVolume decimal.Decimal, currentLevel model.AgentLevel, levels []model.AgentLevel) (*model.AgentLevel, float64, float64, float64, bool) {
	// Find the next level (higher than current level)
	var nextLevel *model.AgentLevel
	for _, level := range levels {
		if level.ID > currentLevel.ID {
			nextLevel = &level
			break
		}
	}

	// If no next level found, user is at max level
	if nextLevel == nil {
		return nil, 0, 0, 0, true
	}

	// Calculate volume requirements for next level
	// User needs to meet either MEME volume OR contract volume threshold
	memeVolumeToNextLevel := 0.0
	contractVolumeToNextLevel := 0.0

	// Check if user already meets MEME volume requirement
	if memeVolume.LessThan(nextLevel.MemeVolumeThreshold) {
		memeVolumeToNextLevel, _ = nextLevel.MemeVolumeThreshold.Sub(memeVolume).Float64()
	}

	// Check if user already meets contract volume requirement
	if contractVolume.LessThan(nextLevel.ContractVolumeThreshold) {
		contractVolumeToNextLevel, _ = nextLevel.ContractVolumeThreshold.Sub(contractVolume).Float64()
	}

	// Total volume needed is the minimum of the two requirements
	volumeToNextLevel := memeVolumeToNextLevel
	if contractVolumeToNextLevel < memeVolumeToNextLevel || memeVolumeToNextLevel == 0 {
		volumeToNextLevel = contractVolumeToNextLevel
	}

	return nextLevel, volumeToNextLevel, memeVolumeToNextLevel, contractVolumeToNextLevel, false
}

// UpdateLevelCommission updates commission rates and meme fee rebate for a specific level
func (s *LevelService) UpdateLevelCommission(ctx context.Context, levelID uint, directRate, indirectRate, extendedRate, memeFeeRebate float64) (*model.AgentLevel, error) {
	// Validate input parameters
	if directRate < 0 || directRate > 1 {
		return nil, fmt.Errorf("direct commission rate must be between 0 and 1")
	}
	if indirectRate < 0 || indirectRate > 1 {
		return nil, fmt.Errorf("indirect commission rate must be between 0 and 1")
	}
	if extendedRate < 0 || extendedRate > 1 {
		return nil, fmt.Errorf("extended commission rate must be between 0 and 1")
	}
	if memeFeeRebate < 0 || memeFeeRebate > 1 {
		return nil, fmt.Errorf("meme fee rebate must be between 0 and 1")
	}

	// Get the current level
	level, err := s.levelRepo.GetAgentLevelByID(ctx, levelID)
	if err != nil {
		return nil, fmt.Errorf("failed to get agent level: %w", err)
	}

	// Update the commission rates and meme fee rebate
	level.DirectCommissionRate = decimal.NewFromFloat(directRate)
	level.IndirectCommissionRate = decimal.NewFromFloat(indirectRate)
	level.ExtendedCommissionRate = decimal.NewFromFloat(extendedRate)
	level.MemeFeeRebate = decimal.NewFromFloat(memeFeeRebate)

	// Save the updated level
	err = s.levelRepo.UpdateAgentLevel(ctx, level)
	if err != nil {
		return nil, fmt.Errorf("failed to update agent level: %w", err)
	}

	return level, nil
}

// calculateMemeFeeRebate calculates the total meme fee rebate earned by a user
func (s *LevelService) calculateMemeFeeRebate(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	err := global.GVA_DB.WithContext(ctx).
		Model(&model.ActivityCashback{}).
		Select("COALESCE(SUM(cashback_amount_usd), 0) as total_amount").
		Where("user_id = ? and status = ?", userID, "PENDING_CLAIM").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate meme fee rebate: %w", err)
	}

	return result.TotalAmount, nil
}

// calculateContractFeeRebate calculates the total contract fee rebate earned by a user
func (s *LevelService) calculateContractFeeRebate(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	err := global.GVA_DB.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ? and status = ?",
			userID, "PENDING_CLAIM").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate contract fee rebate: %w", err)
	}

	return result.TotalAmount, nil
}
