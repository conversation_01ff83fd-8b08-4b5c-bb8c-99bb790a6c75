package transaction

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"go.uber.org/zap"
)

// TransactionDataServiceInterface defines the interface for transaction data operations
type TransactionDataServiceInterface interface {
	GetTransactionData(ctx context.Context, userID uuid.UUID, dataType string, timeRange string) ([]*response.TransactionData, error)
}

// TransactionDataService implements transaction data operations
type TransactionDataService struct {
	affiliateRepo      transaction.AffiliateTransactionRepositoryInterface
	hyperLiquidRepo    transaction.HyperLiquidTransactionRepositoryInterface
	commissionRepo     transaction.CommissionLedgerRepositoryInterface
	memeCommissionRepo transaction.MemeCommissionLedgerRepositoryInterface
	userRepo           transaction.UserRepositoryInterface
}

// NewTransactionDataService creates a new transaction data service
func NewTransactionDataService() TransactionDataServiceInterface {
	return &TransactionDataService{
		affiliateRepo:      transaction.NewAffiliateTransactionRepository(),
		hyperLiquidRepo:    transaction.NewHyperLiquidTransactionRepository(),
		commissionRepo:     transaction.NewCommissionLedgerRepository(),
		memeCommissionRepo: transaction.NewMemeCommissionLedgerRepository(),
		userRepo:           transaction.NewUserRepository(),
	}
}

// GetTransactionData retrieves transaction data based on the specified data type and time range
func (s *TransactionDataService) GetTransactionData(ctx context.Context, userID uuid.UUID, dataType string, timeRange string) ([]*response.TransactionData, error) {
	// Get all direct referrals (level 1)
	directReferrals, err := s.userRepo.GetDirectReferrals(ctx, userID)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get direct referrals, setting to empty slice", zap.Error(err))
		directReferrals = []model.User{}
	}

	// Get all downline users (up to 3 levels)
	allDownlineUsers, err := s.userRepo.GetAllDownlineUsers(ctx, userID, 3)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get downline users, setting to empty slice", zap.Error(err))
		allDownlineUsers = []uuid.UUID{}
	}

	// Calculate time range
	startTime, endTime, err := s.calculateTimeRange(timeRange)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate time range: %w", err)
	}

	// Get transacting user count
	transactingUserCount, err := s.getTransactingUserCount(ctx, userID, startTime, endTime)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get transacting user count, setting to 0", zap.Error(err))
		transactingUserCount = 0
	}

	var transactionAmountUsd string
	var claimedUsd string
	var pendingClaimUsd string
	var contractVolumeUsd string
	var memeVolumeUsd string

	switch dataType {
	case "ALL":
		totalMemeAmount, err := s.getMemeTransactionAmountFromActivityCashback(ctx, allDownlineUsers, startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get total MEME amount from ActivityCashback, setting to 0", zap.Error(err))
			totalMemeAmount = decimal.Zero
		}

		// Calculate total contract transaction amount from HyperLiquidTransaction table
		totalContractAmount, err := s.getContractTransactionAmountFromHyperLiquid(ctx, allDownlineUsers, startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get total contract amount from HyperLiquidTransaction, setting to 0", zap.Error(err))
			totalContractAmount = decimal.Zero
		}

		// Get claimed commission amount for the time period from both tables
		// Contract commission from CommissionLedger table
		claimedContractAmount, err := s.commissionRepo.GetClaimedAmountByUserIDAndTypeAndPeriod(ctx, userID, "CONTRACT", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get claimed contract amount, setting to 0", zap.Error(err))
			claimedContractAmount = decimal.Zero
		}

		// MEME commission from MemeCommissionLedger table
		claimedMemeAmount, err := s.memeCommissionRepo.GetClaimedAmountByUserIDAndTypeAndPeriod(ctx, userID, "MEME", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get claimed MEME amount, setting to 0", zap.Error(err))
			claimedMemeAmount = decimal.Zero
		}

		// Get pending claim amount for the time period from both tables
		// Contract pending commission from CommissionLedger table
		pendingClaimContractAmount, err := s.commissionRepo.GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx, userID, "CONTRACT", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get pending claim contract amount, setting to 0", zap.Error(err))
			pendingClaimContractAmount = decimal.Zero
		}

		// MEME pending commission from MemeCommissionLedger table
		pendingClaimMemeAmount, err := s.memeCommissionRepo.GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx, userID, "MEME", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get pending claim MEME amount, setting to 0", zap.Error(err))
			pendingClaimMemeAmount = decimal.Zero
		}

		// Combine amounts from both tables
		totalAmount := totalMemeAmount.Add(totalContractAmount)
		totalClaimedAmount := claimedContractAmount.Add(claimedMemeAmount)
		totalPendingClaimAmount := pendingClaimContractAmount.Add(pendingClaimMemeAmount)

		transactionAmountUsd = totalAmount.String()
		contractVolumeUsd = totalContractAmount.String()
		memeVolumeUsd = totalMemeAmount.String()
		claimedUsd = totalClaimedAmount.String()
		pendingClaimUsd = totalPendingClaimAmount.String()

	case "MEME":
		// Calculate total MEME transaction amount from ActivityCashback table
		totalMemeAmount, err := s.getMemeTransactionAmountFromActivityCashback(ctx, allDownlineUsers, startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get total MEME amount from ActivityCashback, setting to 0", zap.Error(err))
			totalMemeAmount = decimal.Zero
		}

		// Get claimed MEME commission amount for the time period from MemeCommissionLedger table
		claimedMemeAmount, err := s.memeCommissionRepo.GetClaimedAmountByUserIDAndTypeAndPeriod(ctx, userID, "MEME", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get claimed MEME amount, setting to 0", zap.Error(err))
			claimedMemeAmount = decimal.Zero
		}

		// Get pending claim MEME amount for the time period from MemeCommissionLedger table
		pendingClaimMemeAmount, err := s.memeCommissionRepo.GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx, userID, "MEME", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get pending claim MEME amount, setting to 0", zap.Error(err))
			pendingClaimMemeAmount = decimal.Zero
		}

		memeVolumeUsd = totalMemeAmount.String()
		claimedUsd = claimedMemeAmount.String()
		pendingClaimUsd = pendingClaimMemeAmount.String()

	case "CONTRACT":
		// Calculate total contract transaction amount from HyperLiquidTransaction table
		totalContractAmount, err := s.getContractTransactionAmountFromHyperLiquid(ctx, allDownlineUsers, startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get total contract amount from HyperLiquidTransaction, setting to 0", zap.Error(err))
			totalContractAmount = decimal.Zero 
		}

		// Get claimed contract commission amount for the time period
		claimedContractAmount, err := s.commissionRepo.GetClaimedAmountByUserIDAndTypeAndPeriod(ctx, userID, "CONTRACT", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get claimed contract amount, setting to 0", zap.Error(err))
			claimedContractAmount = decimal.Zero 
		}

		// Get pending claim contract amount for the time period
		pendingClaimContractAmount, err := s.commissionRepo.GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx, userID, "CONTRACT", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get pending claim contract amount, setting to 0", zap.Error(err))
			pendingClaimContractAmount = decimal.Zero 
		}

		contractVolumeUsd = totalContractAmount.String()
		claimedUsd = claimedContractAmount.String()
		pendingClaimUsd = pendingClaimContractAmount.String()

	default:
		return nil, fmt.Errorf("unsupported data type: %s", dataType)
	}

	// Get invitation count for the time period
	invitationCount, err := s.userRepo.GetInvitationCountByUserIDAndPeriod(ctx, userID, startTime, endTime)
	if err != nil {
		global.GVA_LOG.Error("Failed to get invitation count", zap.Error(err))
		// Use fallback to total direct referrals if period-specific count fails
		invitationCount = len(directReferrals)
	}

	// Create transaction data
	result := &response.TransactionData{
		ClaimedUsd:           claimedUsd,
		PendingClaimUsd:      pendingClaimUsd,
		ContractVolumeUsd:    contractVolumeUsd,
		MemeVolumeUsd:        memeVolumeUsd,
		InvitationCount:      invitationCount,
		TransactingUserCount: transactingUserCount,
		TransactionAmountUsd: transactionAmountUsd,
	}

	return []*response.TransactionData{result}, nil
}

// calculateTimeRange calculates the start and end time based on the time range string
func (s *TransactionDataService) calculateTimeRange(timeRange string) (time.Time, time.Time, error) {
	now := time.Now().UTC()
	var startTime time.Time

	switch timeRange {
	case "TODAY":
		startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
	case "LAST_30_DAYS":
		startTime = now.AddDate(0, 0, -30)
	case "LAST_60_DAYS":
		startTime = now.AddDate(0, 0, -60)
	case "ALL_TIME":
		startTime = time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC) // 设置一个足够早的开始时间
	default:
		return time.Time{}, time.Time{}, fmt.Errorf("unsupported time range: %s", timeRange)
	}

	return startTime, now, nil
}

// getTransactingUserCount calculates the number of users who have made transactions within the specified time range
func (s *TransactionDataService) getTransactingUserCount(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) (int, error) {
	// 获取三级用户中在指定时间范围内有交易记录的用户数量
	// 通过 Referral 表关联 User 表，统计三级用户中在时间范围内有交易记录的用户数量

	var count int64
	err := global.GVA_DB.WithContext(ctx).Debug().
		Model(&model.User{}).
		Joins("JOIN referrals ON users.id = referrals.user_id").
		Where("referrals.referrer_id = ? AND referrals.depth <= 3", userID).
		Where("users.first_transaction_at IS NOT NULL").
		Where("users.first_transaction_at >= ? AND users.first_transaction_at < ?", startTime, endTime).
		Count(&count).Error

	if err != nil {
		global.GVA_LOG.Warn("Failed to get transacting user count",
			zap.String("user_id", userID.String()),
			zap.Time("start_time", startTime),
			zap.Time("end_time", endTime),
			zap.Error(err))
		return 0, nil
	}

	return int(count), nil
}

// getMemeTransactionAmountFromActivityCashback calculates the total MEME transaction amount from AffiliateTransaction table
// It calculates the sum of QuoteAmount * SolPriceSnapshot.price for all transactions of level 3 users
func (s *TransactionDataService) getMemeTransactionAmountFromActivityCashback(ctx context.Context, userIDs []uuid.UUID, startTime, endTime time.Time) (decimal.Decimal, error) {
	if len(userIDs) == 0 {
		return decimal.Zero, nil
	}

	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	// Query to calculate total MEME transaction amount
	// Join AffiliateTransaction with SolPriceSnapshot to get the price at transaction time
	// Use the latest price (ORDER BY timestamp DESC) for each transaction
	query := `
		SELECT COALESCE(SUM(
			at.quote_amount * COALESCE(sps.price, 0)
		), 0) as total_amount
		FROM affiliate_transactions at
		LEFT JOIN LATERAL (
			SELECT price 
			FROM sol_price_snapshots 
			WHERE timestamp <= at.created_at 
			ORDER BY timestamp DESC 
			LIMIT 1
		) sps ON true
		WHERE at.user_id IN ?
		AND at.created_at::timestamp >= ?::timestamp 
		AND at.created_at::timestamp < ?::timestamp
		AND at.status = 'Completed'
	`

	err := global.GVA_DB.WithContext(ctx).Debug().
		Raw(query, userIDs, startTimeUTC, endTimeUTC).
		Scan(&result).Error

	if err != nil {
		global.GVA_LOG.Warn("Failed to get total MEME amount from AffiliateTransaction, returning 0", zap.Error(err))
		return decimal.Zero, nil
	}

	return result.TotalAmount, nil
}

// getContractTransactionAmountFromHyperLiquid calculates the total contract transaction amount from HyperLiquidTransaction table
// It calculates the sum of AvgPrice * TotalSz for all transactions of level 3 users
func (s *TransactionDataService) getContractTransactionAmountFromHyperLiquid(ctx context.Context, userIDs []uuid.UUID, startTime, endTime time.Time) (decimal.Decimal, error) {
	if len(userIDs) == 0 {
		return decimal.Zero, nil
	}

	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	// Query to calculate total contract transaction amount
	// Sum of AvgPrice * TotalSz for all HyperLiquid transactions
	query := `
		SELECT COALESCE(SUM(
			COALESCE(hlt.avg_price, 0) * COALESCE(CAST(hlt.total_sz AS DECIMAL), 0)
		), 0) as total_amount
		FROM hyper_liquid_transactions hlt
		WHERE hlt.user_id IN ?
		AND hlt.created_at::timestamp >= ?::timestamp 
		AND hlt.created_at::timestamp < ?::timestamp
		AND hlt.status = 'filled'
		AND hlt.avg_price IS NOT NULL
		AND hlt.total_sz IS NOT NULL
	`

	err := global.GVA_DB.WithContext(ctx).Debug().
		Raw(query, userIDs, startTimeUTC, endTimeUTC).
		Scan(&result).Error

	if err != nil {
		global.GVA_LOG.Warn("Failed to get total contract amount from HyperLiquidTransaction, returning 0", zap.Error(err))
		return decimal.Zero, nil
	}

	return result.TotalAmount, nil
}
