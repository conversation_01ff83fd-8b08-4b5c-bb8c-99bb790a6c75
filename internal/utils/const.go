package utils

const (
	// Agent Referral Tasks
	TaskAgentReferralSnapshot = iota
	TaskLevelUpgrade
	TaskReferralTreeSnapshot
	TaskInfiniteAgentReferralTree
	TaskInfiniteAgentCommission
	// Activity Cashback Tasks
	TaskActivityCashbackDailyReset
	TaskActivityCashbackTierUpgrade
	TaskActivityCashbackProcessing
	TaskActivityCashbackMonthlyReset
	TaskActivityCashbackCleanup
	TaskActivityCashbackCommunityTasks
	// Realtime Volume Sync Tasks
	TaskRealtimeVolumeSync
)

type ChainIDStringType string

const (
	ChainIDSolana ChainIDStringType = "501424"
)

type ChainIDIntType int

const (
	ChainIDSolanaInt      ChainIDIntType = 501424
	ChainIDArbitrumOneInt ChainIDIntType = 42161
)

const WSOL_ADDRESS = "So11111111111111111111111111111111111111112"
const SOL_DECIMALS = 9

const USDC_ARB_ADDRESS = "0xaf88d065e77c8cc2239327c5edb3a432268e5831"
const USDC_ARB_DECIMALS = 6
