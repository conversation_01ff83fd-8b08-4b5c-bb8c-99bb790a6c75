package transaction

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// MemeCommissionLedgerRepositoryInterface defines the interface for meme commission ledger operations
type MemeCommissionLedgerRepositoryInterface interface {
	GetClaimedAmountByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)
	GetPendingClaimAmountByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)
	GetClaimedAmountByUserIDAndType(ctx context.Context, userID uuid.UUID, transactionType string) (decimal.Decimal, error)
	GetPendingClaimAmountByUserIDAndType(ctx context.Context, userID uuid.UUID, transactionType string) (decimal.Decimal, error)
	GetRebateAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error)
	GetClaimedAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error)
	GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error)
	GetPendingCommissionsByUserID(ctx context.Context, userID uuid.UUID) ([]model.MemeCommissionLedger, error)
	CreateMemeCommissionLedger(ctx context.Context, commission *model.MemeCommissionLedger) error
	GetTotalAccumulatedUSDByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)
}

type MemeCommissionLedgerRepository struct {
	db *gorm.DB
}

func NewMemeCommissionLedgerRepository() MemeCommissionLedgerRepositoryInterface {
	return &MemeCommissionLedgerRepository{
		db: global.GVA_DB,
	}
}

// GetClaimedAmountByUserID gets the total claimed meme commission amount for a user
func (r *MemeCommissionLedgerRepository) GetClaimedAmountByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.MemeCommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("status = ?", "CLAIMED").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get claimed meme commission amount: %w", err)
	}

	return result.TotalAmount, nil
}

// GetPendingClaimAmountByUserID gets the total pending claim meme commission amount for a user
func (r *MemeCommissionLedgerRepository) GetPendingClaimAmountByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.MemeCommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("status = ?", "PENDING_CLAIM").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get pending claim meme commission amount: %w", err)
	}

	return result.TotalAmount, nil
}

// GetClaimedAmountByUserIDAndType gets the total claimed meme commission amount for a user by transaction type
func (r *MemeCommissionLedgerRepository) GetClaimedAmountByUserIDAndType(ctx context.Context, userID uuid.UUID, transactionType string) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.MemeCommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("source_transaction_type = ?", transactionType).
		Where("status = ?", "CLAIMED").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get claimed meme commission amount by type: %w", err)
	}

	return result.TotalAmount, nil
}

// GetPendingClaimAmountByUserIDAndType gets the total pending claim meme commission amount for a user by transaction type
func (r *MemeCommissionLedgerRepository) GetPendingClaimAmountByUserIDAndType(ctx context.Context, userID uuid.UUID, transactionType string) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.MemeCommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("source_transaction_type = ?", transactionType).
		Where("status = ?", "PENDING_CLAIM").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get pending claim meme commission amount by type: %w", err)
	}

	return result.TotalAmount, nil
}

// GetRebateAmountByUserIDAndTypeAndPeriod gets the total rebate amount for a user by transaction type within a time period
func (r *MemeCommissionLedgerRepository) GetRebateAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	query := r.db.WithContext(ctx).
		Model(&model.MemeCommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("created_at::timestamp >= ?::timestamp AND created_at::timestamp < ?::timestamp", startTimeUTC, endTimeUTC)

	err := query.Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get rebate meme commission amount by type and period: %w", err)
	}

	return result.TotalAmount, nil
}

// GetClaimedAmountByUserIDAndTypeAndPeriod gets the total claimed meme commission amount for a user by transaction type within a time period
func (r *MemeCommissionLedgerRepository) GetClaimedAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	query := r.db.WithContext(ctx).
		Model(&model.MemeCommissionLedger{}).Debug().
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("status = ?", "CLAIMED").
		Where("created_at::timestamp >= ?::timestamp AND created_at::timestamp < ?::timestamp", startTimeUTC, endTimeUTC)

	err := query.Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get claimed meme commission amount by type and period: %w", err)
	}

	return result.TotalAmount, nil
}

// GetPendingClaimAmountByUserIDAndTypeAndPeriod gets the total pending claim meme commission amount for a user by transaction type within a time period
func (r *MemeCommissionLedgerRepository) GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	query := r.db.WithContext(ctx).
		Model(&model.MemeCommissionLedger{}).Debug().
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("status = ?", "PENDING_CLAIM").
		Where("created_at::timestamp >= ?::timestamp AND created_at::timestamp < ?::timestamp", startTimeUTC, endTimeUTC)

	err := query.Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get pending claim meme commission amount by type and period: %w", err)
	}

	return result.TotalAmount, nil
}

// GetPendingCommissionsByUserID gets all pending meme commissions for a user
func (r *MemeCommissionLedgerRepository) GetPendingCommissionsByUserID(ctx context.Context, userID uuid.UUID) ([]model.MemeCommissionLedger, error) {
	var commissions []model.MemeCommissionLedger

	err := r.db.WithContext(ctx).
		Where("recipient_user_id = ?", userID).
		Where("status = ?", "PENDING_CLAIM").
		Order("created_at DESC").
		Find(&commissions).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get pending meme commissions: %w", err)
	}

	return commissions, nil
}

// CreateMemeCommissionLedger creates a new meme commission ledger record
func (r *MemeCommissionLedgerRepository) CreateMemeCommissionLedger(ctx context.Context, commission *model.MemeCommissionLedger) error {
	if err := r.db.WithContext(ctx).Create(commission).Error; err != nil {
		return fmt.Errorf("failed to create meme commission ledger record: %w", err)
	}
	return nil
}

// GetTotalAccumulatedUSDByUserID gets the total accumulated USD for a user
func (r *MemeCommissionLedgerRepository) GetTotalAccumulatedUSDByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.MemeCommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get total accumulated USD: %w", err)
	}

	return result.TotalAmount, nil
}
